<?php
// Database Setup Script for Mess Management System

require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup - Mess Management System</title>
    <link rel='stylesheet' href='assets/css/style.css'>
</head>
<body>
    <div class='container' style='max-width: 800px; margin: 2rem auto;'>
        <div class='card'>
            <div class='card-header'>
                <h2>Mess Management System Setup</h2>
            </div>
            <div class='card-body'>";

try {
    // Read SQL file
    $sql_file = __DIR__ . '/database/schema.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("SQL schema file not found: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    if ($sql_content === false) {
        throw new Exception("Could not read SQL schema file");
    }
    
    // Connect to MySQL server (without database)
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-info'>Connected to MySQL server successfully.</div>";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "<div class='alert alert-info'>Found " . count($statements) . " SQL statements to execute.</div>";
    
    // Execute each statement
    foreach ($statements as $index => $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
                echo "<div style='color: green; margin: 0.5rem 0;'>✓ Statement " . ($index + 1) . " executed successfully</div>";
            } catch (PDOException $e) {
                // Skip if database already exists
                if (strpos($e->getMessage(), 'database exists') !== false) {
                    echo "<div style='color: orange; margin: 0.5rem 0;'>⚠ Database already exists, skipping creation</div>";
                } else {
                    echo "<div style='color: red; margin: 0.5rem 0;'>✗ Error in statement " . ($index + 1) . ": " . $e->getMessage() . "</div>";
                }
            }
        }
    }
    
    echo "<div class='alert alert-success'>
            <h4>Setup completed successfully!</h4>
            <p>Your Mess Management System is now ready to use.</p>
        </div>";
    
    echo "<div style='background: #f8f9fa; padding: 1rem; border-radius: 5px; margin: 2rem 0;'>
            <h4>Default Login Credentials:</h4>
            <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;'>
                <div>
                    <strong>Admin Login:</strong><br>
                    Username: <code>admin</code><br>
                    Password: <code>admin123</code>
                </div>
                <div>
                    <strong>Server Login:</strong><br>
                    Username: <code>server1</code><br>
                    Password: <code>server123</code>
                </div>
            </div>
        </div>";
    
    echo "<div style='background: #fff3cd; padding: 1rem; border-radius: 5px; border-left: 4px solid #ffc107; margin: 2rem 0;'>
            <h4>Important Notes:</h4>
            <ul style='margin: 0; padding-left: 1.5rem;'>
                <li>Change default passwords after first login</li>
                <li>Update Razorpay credentials in <code>config/config.php</code></li>
                <li>Set proper file permissions for production</li>
                <li>Enable HTTPS for production deployment</li>
                <li>Delete this setup.php file after setup</li>
            </ul>
        </div>";
    
    echo "<div style='text-align: center; margin-top: 2rem;'>
            <a href='index.php' class='btn btn-primary'>Go to Home Page</a>
            <a href='auth/admin_login.php' class='btn btn-secondary'>Admin Login</a>
            <a href='auth/server_login.php' class='btn btn-secondary'>Server Login</a>
        </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h4>Setup Failed!</h4>
            <p>Error: " . htmlspecialchars($e->getMessage()) . "</p>
            <p>Please check your database configuration and try again.</p>
        </div>";
    
    echo "<div style='background: #f8d7da; padding: 1rem; border-radius: 5px; border-left: 4px solid #dc3545; margin: 2rem 0;'>
            <h4>Troubleshooting:</h4>
            <ul style='margin: 0; padding-left: 1.5rem;'>
                <li>Make sure XAMPP/MySQL is running</li>
                <li>Check database credentials in <code>config/database.php</code></li>
                <li>Ensure PHP has PDO MySQL extension enabled</li>
                <li>Verify file permissions</li>
            </ul>
        </div>";
}

echo "            </div>
        </div>
    </div>
</body>
</html>";
?>
