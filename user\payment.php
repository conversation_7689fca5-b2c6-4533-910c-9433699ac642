<?php
$page_title = 'Payment';
$show_nav = true;
$include_razorpay = true;
require_once '../includes/header.php';

// Check if order data exists
if (!isset($_SESSION['order_data'])) {
    $_SESSION['error_message'] = 'No order data found. Please fill the form first.';
    redirect('../user/form.php');
}

$order_data = $_SESSION['order_data'];

// Create Razorpay order
try {
    $conn = getDBConnection();
    
    // Generate payment ID
    $payment_id = 'PAY_' . generateUUID();
    
    // Insert payment record
    $stmt = $conn->prepare("
        INSERT INTO payments (payment_id, user_id, amount, status) 
        VALUES (?, ?, ?, 'created')
    ");
    $stmt->execute([$payment_id, $order_data['user_id'], $order_data['amount']]);
    
    // Store payment ID in session
    $_SESSION['payment_id'] = $payment_id;
    
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Payment initialization failed. Please try again.';
    logActivity("Payment initialization error: " . $e->getMessage());
    redirect('../user/form.php');
}
?>

<div class="card">
    <div class="card-header">
        <h2>Payment</h2>
    </div>
    <div class="card-body">
        <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 5px; margin-bottom: 2rem;">
            <h3 style="margin: 0 0 1rem 0; color: #667eea;">Order Details</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div>
                    <strong>Name:</strong> <?php echo htmlspecialchars($order_data['name']); ?>
                </div>
                <div>
                    <strong>UID:</strong> <?php echo htmlspecialchars($order_data['uid']); ?>
                </div>
                <div>
                    <strong>Phone:</strong> <?php echo htmlspecialchars($order_data['phone']); ?>
                </div>
                <div>
                    <strong>Amount:</strong> <?php echo formatCurrency($order_data['amount']); ?>
                </div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button id="pay-button" class="btn btn-success" style="font-size: 1.2rem; padding: 1rem 2rem;">
                Pay <?php echo formatCurrency($order_data['amount']); ?>
            </button>
        </div>
        
        <div style="text-align: center; margin-top: 1rem;">
            <a href="form.php" class="btn btn-secondary">Back to Form</a>
        </div>
        
        <div style="margin-top: 2rem; padding: 1rem; background: #e7f3ff; border-radius: 5px; border-left: 4px solid #007bff;">
            <h4 style="margin: 0 0 0.5rem 0; color: #007bff;">Payment Information</h4>
            <ul style="margin: 0; padding-left: 1.5rem; color: #666;">
                <li>Your payment is secured by Razorpay</li>
                <li>You can pay using UPI, Cards, Net Banking, or Wallets</li>
                <li>After successful payment, you'll receive a unique token</li>
                <li>Show this token at the mess counter to collect your food</li>
            </ul>
        </div>
    </div>
</div>

<script>
document.getElementById('pay-button').addEventListener('click', function() {
    // Show loading
    this.disabled = true;
    this.innerHTML = '<span class="loading"></span> Processing...';
    
    // In a real implementation, you would create a Razorpay order on the server
    // For demo purposes, we'll simulate the payment process
    
    // Simulate payment processing
    setTimeout(() => {
        // For demo, we'll redirect to success page
        // In real implementation, this would be handled by Razorpay callback
        window.location.href = 'payment_success.php?payment_id=<?php echo $payment_id; ?>&demo=true';
    }, 2000);
    
    /* Real Razorpay integration would look like this:
    
    var options = {
        "key": "<?php echo RAZORPAY_KEY_ID; ?>",
        "amount": <?php echo $order_data['amount'] * 100; ?>, // Amount in paise
        "currency": "INR",
        "name": "<?php echo SITE_NAME; ?>",
        "description": "Mess Food Payment",
        "order_id": "order_id_from_server", // This should be created on server
        "handler": function (response) {
            // Payment successful
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = 'payment_success.php';
            
            var fields = {
                'razorpay_payment_id': response.razorpay_payment_id,
                'razorpay_order_id': response.razorpay_order_id,
                'razorpay_signature': response.razorpay_signature,
                'payment_id': '<?php echo $payment_id; ?>'
            };
            
            for (var key in fields) {
                var input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = fields[key];
                form.appendChild(input);
            }
            
            document.body.appendChild(form);
            form.submit();
        },
        "prefill": {
            "name": "<?php echo htmlspecialchars($order_data['name']); ?>",
            "email": "<?php echo htmlspecialchars($order_data['email']); ?>",
            "contact": "<?php echo htmlspecialchars($order_data['phone']); ?>"
        },
        "theme": {
            "color": "#667eea"
        },
        "modal": {
            "ondismiss": function() {
                // Payment cancelled
                document.getElementById('pay-button').disabled = false;
                document.getElementById('pay-button').innerHTML = 'Pay <?php echo formatCurrency($order_data['amount']); ?>';
            }
        }
    };
    
    var rzp = new Razorpay(options);
    rzp.open();
    */
});
</script>

<?php require_once '../includes/footer.php'; ?>
