<?php
$page_title = 'Welcome';
$show_nav = true;
require_once 'includes/header.php';
?>

<div class="qr-section">
    <div class="card">
        <div class="card-header">
            <h2>Welcome to Mess Management System</h2>
        </div>
        <div class="card-body">
            <p style="text-align: center; font-size: 1.2rem; margin-bottom: 2rem;">Scan the QR code or click the button below to order your meal</p>

            <div class="qr-code">
                <!-- QR Code will be generated here -->
                <div style="width: 200px; height: 200px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; margin: 0 auto; border: 2px dashed #ccc;">
                    <span style="color: #666;">QR Code</span>
                </div>
                <p style="margin-top: 1rem; color: #666;">Scan this QR code with your phone</p>
            </div>

            <div style="text-align: center; margin-top: 2rem;">
                <a href="user/form.php" class="btn btn-primary" style="font-size: 1.2rem; padding: 1rem 2rem;">Order Food - ₹<?php echo MEAL_PRICE; ?></a>
            </div>

            <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee;">
                <h3>How it works:</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <div style="text-align: center; padding: 1rem;">
                        <div style="background: #667eea; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 1.5rem; font-weight: bold;">1</div>
                        <h4>Fill Form</h4>
                        <p>Enter your details</p>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <div style="background: #667eea; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 1.5rem; font-weight: bold;">2</div>
                        <h4>Make Payment</h4>
                        <p>Pay securely online</p>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <div style="background: #667eea; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 1.5rem; font-weight: bold;">3</div>
                        <h4>Get Token</h4>
                        <p>Receive unique token</p>
                    </div>
                    <div style="text-align: center; padding: 1rem;">
                        <div style="background: #667eea; color: white; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 1.5rem; font-weight: bold;">4</div>
                        <h4>Collect Food</h4>
                        <p>Show token to get food</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Generate QR code for the form URL
document.addEventListener('DOMContentLoaded', function() {
    const qrContainer = document.querySelector('.qr-code div');
    const formUrl = '<?php echo SITE_URL; ?>/user/form.php';

    // You can integrate a QR code library here
    // For now, showing a placeholder
    qrContainer.innerHTML = `
        <div style="font-size: 0.9rem; color: #333;">
            <strong>QR Code URL:</strong><br>
            <small>${formUrl}</small>
        </div>
    `;
});
</script>

<?php require_once 'includes/footer.php'; ?>